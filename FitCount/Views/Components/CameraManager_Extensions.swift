import SwiftUI
import AVFoundation
import MediaPipeTasksVision
import CoreMedia

// MARK: - CameraManager 扩展：摄像头切换功能

extension CameraManager {

    /// 切换前后摄像头
    func toggleCamera() {
        DebugLogger.info("切换前后摄像头")

        DispatchQueue.main.async { [weak self] in
            self?.debugState = "正在切换摄像头..."
        }

        sessionQueue.async { [weak self] in
            guard let self = self else { return }

            DebugLogger.debug("在sessionQueue中开始切换摄像头")

            // 开始配置
            self.session.beginConfiguration()

            // 获取当前输入
            guard let currentInput = self.session.inputs.first as? AVCaptureDeviceInput else {
                DebugLogger.error("无法获取当前摄像头输入")
                self.session.commitConfiguration()

                DispatchQueue.main.async { [weak self] in
                    self?.sessionError = "无法获取当前摄像头，切换失败"
                    self?.debugState = "切换摄像头失败"
                }
                return
            }

            // 获取当前摄像头位置
            let currentPosition = currentInput.device.position
            DebugLogger.debug("当前摄像头位置: \(currentPosition == .front ? "前置" : "后置")")

            // 确定新的摄像头位置
            let newPosition: AVCaptureDevice.Position = (currentPosition == .front) ? .back : .front
            DebugLogger.debug("目标摄像头位置: \(newPosition == .front ? "前置" : "后置")")

            // 移除当前输入
            self.session.removeInput(currentInput)
            DebugLogger.debug("已移除当前摄像头输入")

            // 查找新摄像头设备
            guard let newVideoDevice = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: newPosition) else {
                let errorMsg = "无法找到\(newPosition == .front ? "前置" : "后置")摄像头"
                DebugLogger.error(errorMsg)

                // 尝试恢复原始摄像头
                if self.session.canAddInput(currentInput) {
                    self.session.addInput(currentInput)
                    DebugLogger.debug("恢复使用原始摄像头")
                }

                self.session.commitConfiguration()

                DispatchQueue.main.async { [weak self] in
                    self?.sessionError = errorMsg
                    self?.debugState = errorMsg
                }
                return
            }

            // 创建新的输入
            do {
                let newVideoInput = try AVCaptureDeviceInput(device: newVideoDevice)

                // 检查是否可以添加
                if self.session.canAddInput(newVideoInput) {
                    // 添加新的输入
                    self.session.addInput(newVideoInput)
                    DebugLogger.debug("成功添加新摄像头输入")

                    // 更新UI状态（在提交配置后）
                    DispatchQueue.main.async { [weak self] in
                        guard let self = self else { return }
                        self.isUsingFrontCamera = (newPosition == .front)
                        self.debugState = "已切换到\(newPosition == .front ? "前置" : "后置")摄像头"
                    }
                } else {
                    let errorMsg = "无法添加\(newPosition == .front ? "前置" : "后置")摄像头到会话"
                    DebugLogger.error(errorMsg)

                    // 尝试恢复原始摄像头
                    if self.session.canAddInput(currentInput) {
                        self.session.addInput(currentInput)
                        DebugLogger.debug("恢复使用原始摄像头")
                    }

                    DispatchQueue.main.async { [weak self] in
                        self?.sessionError = errorMsg
                        self?.debugState = errorMsg
                    }
                }
            } catch {
                let errorMsg = "创建\(newPosition == .front ? "前置" : "后置")摄像头输入时出错: \(error.localizedDescription)"
                DebugLogger.error(errorMsg)

                // 尝试恢复原始摄像头
                if self.session.canAddInput(currentInput) {
                    self.session.addInput(currentInput)
                    DebugLogger.debug("恢复使用原始摄像头")
                }

                DispatchQueue.main.async { [weak self] in
                    self?.sessionError = errorMsg
                    self?.debugState = errorMsg
                }
            }

            // 提交配置
            self.session.commitConfiguration()
            DebugLogger.debug("摄像头切换配置已提交")
        }
    }

    /// 检查并打印相机状态（调试方法）
    func checkCameraStatus() {
        let debugInfo = """
        ======= 相机状态检查 =======
        会话正在运行: \(session.isRunning)
        isSessionRunning: \(isSessionRunning)
        会话被中断: \(isSessionInterrupted)
        前置摄像头: \(isUsingFrontCamera)
        预览层: \(preview != nil ? "已创建" : "未创建")
        错误信息: \(sessionError ?? "无")
        当前状态: \(debugState)
        输入数量: \(session.inputs.count)
        输出数量: \(session.outputs.count)
        ============================
        """

        DebugLogger.info(debugInfo)
    }
}

// MARK: - CameraManager 扩展：通知观察者管理

extension CameraManager {

    /// 设置监听各种相机会话状态的观察者
    func setupObservers() {
        DebugLogger.debug("设置会话状态观察者")

        // 监听会话被中断
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionWasInterrupted),
            name: .AVCaptureSessionWasInterrupted,
            object: session
        )

        // 监听会话中断结束
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionInterruptionEnded),
            name: .AVCaptureSessionInterruptionEnded,
            object: session
        )

        // 监听会话运行错误
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionRuntimeError),
            name: .AVCaptureSessionRuntimeError,
            object: session
        )

        // 监听应用进入后台
        notificationCenter.addObserver(
            self,
            selector: #selector(applicationWillResignActive),
            name: UIScene.willDeactivateNotification,
            object: nil
        )

        // 监听应用回到前台
        notificationCenter.addObserver(
            self,
            selector: #selector(applicationDidBecomeActive),
            name: UIScene.didActivateNotification,
            object: nil
        )

        // 监听会话开始运行
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionDidStartRunning),
            name: .AVCaptureSessionDidStartRunning,
            object: session
        )

        // 监听会话停止运行
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionDidStopRunning),
            name: .AVCaptureSessionDidStopRunning,
            object: session
        )

        // 监听设备方向变化
        notificationCenter.addObserver(
            self,
            selector: #selector(deviceOrientationDidChange),
            name: UIDevice.orientationDidChangeNotification,
            object: nil
        )
    }

    /// 移除所有观察者
    func removeObservers() {
        DebugLogger.debug("移除会话状态观察者")
        notificationCenter.removeObserver(self)
        sessionRunningObservation?.invalidate()
        sessionRunningObservation = nil
    }
}

// MARK: - CameraManager 扩展：通知处理方法

extension CameraManager {

    /// 相机会话被中断
    @objc private func sessionWasInterrupted(notification: NSNotification) {
        DebugLogger.warning("相机会话被中断")

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.isSessionInterrupted = true
            self.debugState = "相机会话被中断"
        }

        // 判断中断原因
        if let userInfoValue = notification.userInfo?[AVCaptureSessionInterruptionReasonKey] as AnyObject?,
           let reasonIntegerValue = userInfoValue.integerValue,
           let reason = AVCaptureSession.InterruptionReason(rawValue: reasonIntegerValue) {
            DebugLogger.warning("相机会话被中断，原因: \(reason)")

            var reasonString = "未知原因"
            var showResumeButton = false

            switch reason {
            case .videoDeviceNotAvailableWithMultipleForegroundApps:
                reasonString = "多个前台应用使用摄像头"
                showResumeButton = false
            case .videoDeviceInUseByAnotherClient:
                reasonString = "摄像头被其他应用使用"
                showResumeButton = false
            case .videoDeviceNotAvailableDueToSystemPressure:
                reasonString = "系统压力导致摄像头不可用"
                showResumeButton = false
            case .audioDeviceInUseByAnotherClient:
                reasonString = "音频设备被其他应用使用"
                showResumeButton = false
            case .videoDeviceNotAvailableInBackground:
                // 在后台时摄像头不可用
                reasonString = "应用在后台时摄像头不可用"
                showResumeButton = true
            @unknown default:
                reasonString = "未知原因(\(reason.rawValue))"
                showResumeButton = false
            }

            DispatchQueue.main.async { [weak self] in
                self?.debugState = "相机中断: \(reasonString)"
            }

            if showResumeButton {
                // 在这里可以展示重新开始按钮或提示
                DebugLogger.debug("应该显示恢复按钮")
            }
        }
    }

    /// 相机会话中断结束
    @objc private func sessionInterruptionEnded(notification: NSNotification) {
        DebugLogger.info("相机会话中断结束")

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.isSessionInterrupted = false
            self.debugState = "相机会话恢复"

            // 如果会话不在运行，尝试重新启动
            if !self.session.isRunning {
                DebugLogger.debug("中断结束后尝试重新启动会话")
                self.startSession()
            }
        }
    }

    /// 相机会话运行错误
    @objc private func sessionRuntimeError(notification: NSNotification) {
        guard let error = notification.userInfo?[AVCaptureSessionErrorKey] as? AVError else {
            DebugLogger.error("相机会话运行错误，但无法获取错误详情")
            return
        }

        DebugLogger.error("相机会话运行错误: \(error.localizedDescription) (Code: \(error.code.rawValue))")

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.sessionError = error.localizedDescription
            self.debugState = "相机错误: \(error.localizedDescription)"
        }

        // 根据错误类型进行不同处理
        if error.code == .mediaServicesWereReset {
            DebugLogger.info("媒体服务被重置，尝试重新建立会话")
            // 媒体服务被重置，可以尝试重新建立会话
            sessionQueue.async { [weak self] in
                guard let self = self else { return }
                self.stopSession()
                self.setupAndStartSession()
            }
        } else {
            // 其他错误，暂时不做特殊处理
            DebugLogger.warning("未处理的相机错误: \(error.code)")
        }
    }

    /// 会话开始运行通知
    @objc private func sessionDidStartRunning(notification: NSNotification) {
        DebugLogger.info("收到会话开始运行通知")
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.isSessionRunning = true
            self.debugState = "相机会话运行中"
        }
    }

    /// 会话停止运行通知
    @objc private func sessionDidStopRunning(notification: NSNotification) {
        DebugLogger.info("收到会话停止运行通知")
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.isSessionRunning = false
            self.debugState = "相机会话已停止"
        }
    }

    /// 应用进入后台
    @objc private func applicationWillResignActive(notification: NSNotification) {
        DebugLogger.info("应用进入后台")
        if isSessionRunning {
            DebugLogger.debug("应用进入后台，停止相机会话")
            stopSession()
        }
    }

    /// 应用回到前台
    @objc private func applicationDidBecomeActive(notification: NSNotification) {
        DebugLogger.info("应用回到前台")
        if !isSessionRunning && !isSessionInterrupted {
            DebugLogger.debug("应用回到前台，尝试启动相机会话")
            setupAndStartSession()
        }
    }

    /// 设备方向变化处理
    @objc private func deviceOrientationDidChange(notification: NSNotification) {
        let currentOrientation = UIDevice.current.orientation
        let orientationName = getOrientationName(currentOrientation)

        DebugLogger.debug("CameraManager 检测到设备方向变化: \(orientationName)")

        // 更新预览层的视频方向（如果需要）
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            if let previewLayer = self.preview,
               let connection = previewLayer.connection,
               connection.isVideoOrientationSupported {

                let videoOrientation = self.getVideoOrientation(from: currentOrientation)
                if connection.videoOrientation != videoOrientation {
                    connection.videoOrientation = videoOrientation
                    DebugLogger.debug("更新预览层视频方向: \(videoOrientation)")
                }
            }
        }
    }

    /// 获取方向的本地化名称
    private func getOrientationName(_ orientation: UIDeviceOrientation) -> String {
        switch orientation {
        case .portrait:
            return NSLocalizedString("orientation.portrait", comment: "竖屏")
        case .landscapeLeft:
            return NSLocalizedString("orientation.landscape.left", comment: "横屏左")
        case .landscapeRight:
            return NSLocalizedString("orientation.landscape.right", comment: "横屏右")
        default:
            return NSLocalizedString("orientation.unknown", comment: "未知方向")
        }
    }

    /// 将设备方向转换为视频方向
    private func getVideoOrientation(from deviceOrientation: UIDeviceOrientation) -> AVCaptureVideoOrientation {
        switch deviceOrientation {
        case .portrait:
            return .portrait
        case .landscapeLeft:
            return .landscapeRight
        case .landscapeRight:
            return .landscapeLeft
        case .portraitUpsideDown:
            return .portraitUpsideDown
        default:
            return .portrait
        }
    }
}

// MARK: - CameraManager 扩展：MediaPipe 姿态检测代理

extension CameraManager: PoseLandmarkerServiceLiveStreamDelegate {

    func poseLandmarkerService(
        _ poseLandmarkerService: PoseLandmarkerService,
        didFinishDetection result: ResultBundle?,
        error: Error?) {
            DebugLogger.info("poseLandmarkerService")

            // 处理人体姿态检测结果并绘制关键点
            guard let result = result,
                  let poseLandmarkerResult = result.poseLandmarkerResults.first as? PoseLandmarkerResult,
                  !poseLandmarkerResult.landmarks.isEmpty else {
                return
            }

            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }

                // 获取第一个检测到的人体的关键点
                let landmarks = poseLandmarkerResult.landmarks[0]

                // 使用仰卧起坐计数器处理检测结果
                if !poseLandmarkerResult.worldLandmarks.isEmpty {
                    let worldLandmarks = poseLandmarkerResult.worldLandmarks[0]
                    let currentOrientation = UIDevice.current.orientation
                    sitUpCounter.processLandmarks(worldLandmarks, deviceOrientation: currentOrientation)
                }


                // 如果有 overlay view，更新绘制
                if let overlayView = self.poseOverlayView {
                    // 尝试使用预览层增强坐标转换
                    if let previewLayer = self.preview {
                        // 使用预览层的精确坐标转换
                        let poseOverlayData = PoseOverlayView.createPoseOverlayDataWithPreviewLayer(
                            fromPoseLandmarks: landmarks,
                            previewLayer: previewLayer,
                            overlayViewSize: overlayView.bounds.size,
                            isFrontCamera: self.isUsingFrontCamera
                        )

                        // 更新 overlay view（使用预览层尺寸）
                        overlayView.updateOverlay(
                            poseOverlayData: poseOverlayData,
                            inBoundsOfContentImageOfSize: previewLayer.bounds.size,
                            imageContentMode: previewLayer.videoGravity.contentMode
                        )
                    } else {
                        // 回退到传统方法
                        let (imageSize, contentMode) = self.calculateImageSizeAndContentMode(for: overlayView)
                        let currentOrientation = UIImage.Orientation.from(deviceOrientation: UIDevice.current.orientation)

                        DebugLogger.debug("回退到传统坐标转换 - 图像尺寸: \(imageSize), 内容模式: \(contentMode), 方向: \(currentOrientation)")

                        let poseOverlayData = PoseOverlayView.createPoseOverlayData(
                            fromPoseLandmarks: landmarks,
                            inferredOnImageOfSize: imageSize,
                            overlayViewSize: overlayView.bounds.size,
                            imageContentMode: contentMode,
                            andOrientation: currentOrientation,
                            isFrontCamera: self.isUsingFrontCamera
                        )

                        overlayView.updateOverlay(
                            poseOverlayData: poseOverlayData,
                            inBoundsOfContentImageOfSize: imageSize,
                            imageContentMode: contentMode
                        )
                    }
                }
            }





        }

    /// 计算精确的图像尺寸和内容模式
    /// - Parameter overlayView: 姿态叠加视图
    /// - Returns: 包含图像尺寸和内容模式的元组
    private func calculateImageSizeAndContentMode(for overlayView: PoseOverlayView) -> (CGSize, UIView.ContentMode) {
        var imageSize = overlayView.bounds.size
        var contentMode: UIView.ContentMode = .scaleAspectFill

        // 如果有预览层，使用其设置
        if let previewLayer = self.preview {
            contentMode = previewLayer.videoGravity.contentMode

            // 优先使用预览层的实际尺寸
            if previewLayer.bounds.size.width > 0 && previewLayer.bounds.size.height > 0 {
                imageSize = previewLayer.bounds.size
                DebugLogger.debug("使用预览层尺寸: \(imageSize)")
            } else {
                // 如果预览层尺寸无效，尝试从相机格式获取
                imageSize = getCameraFormatSize() ?? overlayView.bounds.size
                DebugLogger.debug("使用相机格式尺寸: \(imageSize)")
            }
        } else {
            // 没有预览层时，使用叠加视图的尺寸
            DebugLogger.debug("使用叠加视图尺寸: \(imageSize)")
        }

        return (imageSize, contentMode)
    }

    /// 获取相机格式尺寸
    /// - Returns: 相机格式的尺寸，如果无法获取则返回nil
    private func getCameraFormatSize() -> CGSize? {
        guard let videoInput = self.session.inputs.first as? AVCaptureDeviceInput else {
            DebugLogger.warning("无法获取视频输入设备")
            return nil
        }

        let device = videoInput.device
        let format = device.activeFormat
        let dimensions = CMVideoFormatDescriptionGetDimensions(format.formatDescription)

        // 相机传感器通常是横向的，根据当前设备方向调整尺寸
        let currentOrientation = UIDevice.current.orientation
        let cameraSize = CGSize(width: Int(dimensions.width), height: Int(dimensions.height))

        // 根据设备方向调整图像尺寸
        switch currentOrientation {
        case .portrait, .portraitUpsideDown:
            // 竖屏时，交换宽高以匹配显示方向
            return CGSize(width: cameraSize.height, height: cameraSize.width)
        case .landscapeLeft, .landscapeRight:
            // 横屏时，保持原始尺寸
            return cameraSize
        default:
            // 默认情况，假设竖屏
            return CGSize(width: cameraSize.height, height: cameraSize.width)
        }
    }
}

// MARK: - CameraManager 扩展：视频数据输出代理

extension CameraManager: AVCaptureVideoDataOutputSampleBufferDelegate {

    /// 处理相机输出的视频帧数据
    /// - Parameters:
    ///   - output: 输出对象
    ///   - sampleBuffer: 样本缓冲区，包含视频帧数据
    ///   - connection: 连接对象
    func captureOutput(_ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        guard CMSampleBufferGetImageBuffer(sampleBuffer) != nil else { return }

        // 获取当前设备方向
        let orientation = UIImage.Orientation.from(deviceOrientation: UIDevice.current.orientation)

        // 获取当前时间戳（毫秒）
        let currentTimeMs = Date().timeIntervalSince1970 * 1000

        // 在后台队列中将像素缓冲区传递给MediaPipe进行姿态检测
        backgroundQueue.async { [weak self] in
            guard let weakself = self else { return }
            weakself.fieldPoseLandmarkerService?.detectAsync(
              sampleBuffer: sampleBuffer,
              orientation: orientation,
              timeStamps: Int(currentTimeMs))
        }
    }
}

# DebugLogger 使用说明

## 概述

DebugLogger 是基于 Apple 官方 os_log 框架的高性能日志工具类，提供了完整的日志记录功能，支持在 Xcode 控制台和 macOS Console.app 中查看日志。

## 主要特性

- ✅ **高性能**：使用 os_log 的延迟字符串插值技术
- ✅ **系统集成**：与 Console.app 和 Instruments 完美集成
- ✅ **隐私保护**：自动处理敏感信息的隐私保护
- ✅ **分类管理**：支持 subsystem 和 category 进行精细化日志分类
- ✅ **向后兼容**：保持原有 API 接口不变

## 基本使用

### 1. 基础日志记录

```swift
// 基本日志记录
DebugLogger.info("应用启动完成")
DebugLogger.debug("用户点击了开始按钮")
DebugLogger.warning("网络连接不稳定")
DebugLogger.error("无法加载用户数据")

// 带分类的日志记录
DebugLogger.info("相机初始化完成", category: "Camera")
DebugLogger.debug("检测到人体姿态", category: "PoseDetection")
```

### 2. 专用分类方法

```swift
// 相机相关日志
DebugLogger.camera("相机权限已获取", level: .info)

// 姿态检测相关日志
DebugLogger.poseDetection("检测到 33 个关键点", level: .debug)

// 运动检测相关日志
DebugLogger.motion("陀螺仪数据更新", level: .verbose)

// 计数器相关日志
DebugLogger.counter("仰卧起坐计数: 15", level: .info)

// UI 相关日志
DebugLogger.ui("页面切换到运动模式", level: .info)
```

### 3. 强制可见日志

```swift
// 确保在 Console.app 中可见的重要日志
DebugLogger.forceLog("关键错误信息")
DebugLogger.appLaunch("应用启动耗时: 2.3秒")
DebugLogger.performance("内存使用: 45MB")
DebugLogger.network("API 请求失败: 超时")
```

## 在 Console.app 中查看日志

### 为什么在 Console.app 中看不到日志？

1. **日志级别过滤**：系统会根据日志级别自动过滤
2. **隐私保护**：某些信息可能被隐私保护机制过滤
3. **模拟器 vs 真机**：不同环境的日志行为不同
4. **系统设置**：macOS 的日志显示设置影响

### 解决方案

#### 1. 在 Console.app 中正确搜索

1. 打开 Console.app（应用程序 > 实用工具 > 控制台）
2. 在搜索框中输入：`com.jack.FitCount`
3. 选择正确的设备（iPhone 模拟器或真机）
4. 确保时间范围正确

#### 2. 使用强制日志方法

```swift
// 使用 forceLog 确保日志可见
DebugLogger.forceLog("这条日志一定会在 Console.app 中显示")

// 测试所有日志级别
DebugLogger.testAllLevels()

// 查看当前配置
DebugLogger.printConfiguration()
```

#### 3. 调整日志级别映射

我们已经优化了日志级别映射，确保更好的可见性：

- `error` → `OSLogType.fault`（最高优先级，红色显示）
- `warning` → `OSLogType.error`（错误级别，确保可见）
- `info` → `OSLogType.default`（默认级别，正常显示）
- `debug` → `OSLogType.info`（提升为 info 级别，确保可见）
- `verbose` → `OSLogType.debug`（详细信息）

## 配置选项

### 1. 启用/禁用日志

```swift
// 全局启用/禁用
DebugLogger.isEnabled = true  // 默认启用
DebugLogger.isEnabled = false // 禁用所有日志
```

### 2. 设置日志级别

```swift
// 设置日志级别（只记录指定级别及以上的日志）
DebugLogger.logLevel = .error   // 只记录错误
DebugLogger.logLevel = .warning // 记录警告和错误
DebugLogger.logLevel = .info    // 记录信息、警告和错误
DebugLogger.logLevel = .debug   // 记录调试及以上（默认）
DebugLogger.logLevel = .verbose // 记录所有日志
```

## 最佳实践

### 1. 选择合适的日志级别

- `error`：严重错误，需要立即关注
- `warning`：潜在问题，需要注意
- `info`：重要信息，正常运行状态
- `debug`：调试信息，开发时使用
- `verbose`：详细信息，深度调试时使用

### 2. 使用合适的分类

```swift
// 推荐的分类使用
DebugLogger.camera("相机相关操作")
DebugLogger.poseDetection("姿态检测相关")
DebugLogger.motion("设备运动相关")
DebugLogger.counter("计数器相关")
DebugLogger.ui("用户界面相关")

// 或者使用自定义分类
DebugLogger.info("自定义消息", category: "CustomCategory")
```

### 3. 性能考虑

```swift
// ✅ 推荐：os_log 会自动优化字符串插值
DebugLogger.debug("用户ID: \(userID), 状态: \(status)")

// ❌ 不推荐：手动字符串拼接
let message = "用户ID: " + userID + ", 状态: " + status
DebugLogger.debug(message)
```

## 故障排除

### 1. 测试日志系统

```swift
// 在应用启动时测试日志系统
DebugLogger.testAllLevels()
```

### 2. 检查配置

```swift
// 打印当前配置信息
DebugLogger.printConfiguration()
```

### 3. 清理缓存

```swift
// 在内存警告时清理缓存
DebugLogger.clearCache()
```

## 迁移指南

从旧版本迁移到新版本非常简单，因为我们保持了 API 兼容性：

```swift
// 旧代码仍然可以正常工作
DebugLogger.log("消息", level: .info)
DebugLogger.error("错误消息")
DebugLogger.debug("调试消息")

// 新功能
DebugLogger.forceLog("强制显示的消息")
DebugLogger.camera("相机消息")
DebugLogger.testAllLevels()
```

## 注意事项

1. **隐私保护**：敏感信息会被自动处理，如需显示敏感信息，请使用 `%{public}@` 格式
2. **性能影响**：os_log 的性能开销很小，但仍建议在生产环境中适当控制日志级别
3. **存储管理**：系统会自动管理日志存储和清理，无需手动处理
4. **版本兼容**：需要 iOS 10.0+ 才能使用 os_log 框架

## 技术支持

如果遇到问题，请：

1. 首先运行 `DebugLogger.testAllLevels()` 测试日志系统
2. 检查 Console.app 的搜索和过滤设置
3. 确认设备选择正确（模拟器 vs 真机）
4. 使用 `DebugLogger.forceLog()` 记录关键信息

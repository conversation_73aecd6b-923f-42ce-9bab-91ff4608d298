import Foundation
import os.log

/// DebugLogger - 基于 Apple os_log 框架的调试日志工具类
///
/// 使用 Apple 官方推荐的 os_log 框架进行日志记录，具有以下优势：
/// - 高性能：延迟字符串插值，只有在实际需要时才格式化字符串
/// - 系统集成：与 Console.app 和 Instruments 完美集成
/// - 隐私保护：自动处理敏感信息的隐私保护
/// - 分类管理：支持 subsystem 和 category 进行精细化日志分类
/// - 存储优化：系统自动管理日志存储和清理
class DebugLogger {

    // MARK: - OSLog 配置

    /// 应用程序的子系统标识符，用于在系统日志中区分不同应用
    private static let subsystem = "com.jack.FitCount"

    /// 默认日志分类，用于一般性日志记录
    private static let defaultCategory = "General"

    /// 默认 OSLog 实例，用于一般性日志记录
    private static let defaultLogger = OSLog(subsystem: subsystem, category: defaultCategory)

    /// 不同模块的 OSLog 实例缓存，提高性能
    private static var loggers: [String: OSLog] = [:]

    /// 获取指定分类的 OSLog 实例
    /// - Parameter category: 日志分类名称（如 "Camera", "PoseDetection", "Motion" 等）
    /// - Returns: 对应分类的 OSLog 实例
    private static func logger(for category: String) -> OSLog {
        if let existingLogger = loggers[category] {
            return existingLogger
        }

        let newLogger = OSLog(subsystem: subsystem, category: category)
        loggers[category] = newLogger
        return newLogger
    }

    // MARK: - 兼容性配置

    /// 是否启用日志功能（保持向后兼容）
    /// 注意：os_log 的启用/禁用主要通过系统设置控制，此选项用于应用层面的额外控制
    static var isEnabled = true

    /// 当前日志级别，只有小于或等于此级别的日志会被记录
    static var logLevel: LogLevel = .debug

    /// 日志级别枚举，按照严重程度从高到低排序
    /// 映射到对应的 OSLogType 以充分利用系统日志功能
    enum LogLevel: Int, CaseIterable {
        case error = 0   // 错误：表示发生了严重问题，映射到 .error
        case warning = 1 // 警告：表示潜在问题，映射到 .default
        case info = 2    // 信息：表示重要但非错误信息，映射到 .info
        case debug = 3   // 调试：用于调试目的的详细信息，映射到 .debug
        case verbose = 4 // 详细：最详细的日志信息，映射到 .debug

        /// 将自定义日志级别映射到 OSLogType
        var osLogType: OSLogType {
            switch self {
            case .error:
                return .error      // 错误级别，红色显示，会被持久化存储
            case .warning:
                return .default    // 默认级别，用于警告信息
            case .info:
                return .info       // 信息级别，用于一般信息记录
            case .debug, .verbose:
                return .debug      // 调试级别，仅在调试模式下显示
            }
        }

        /// 日志级别的可读描述
        var description: String {
            switch self {
            case .error: return "ERROR"
            case .warning: return "WARNING"
            case .info: return "INFO"
            case .debug: return "DEBUG"
            case .verbose: return "VERBOSE"
            }
        }

        /// 日志级别对应的 emoji 图标（用于控制台输出）
        var emoji: String {
            switch self {
            case .error: return "❌"
            case .warning: return "⚠️"
            case .info: return "ℹ️"
            case .debug: return "🔍"
            case .verbose: return "📝"
            }
        }
    }

    // MARK: - 核心日志记录方法

    /// 主要日志记录方法 - 使用 os_log 进行高性能日志记录
    ///
    /// 此方法是所有日志记录的核心，使用 Apple 的 os_log 框架进行日志记录。
    /// os_log 具有以下优势：
    /// - 延迟字符串插值：只有在日志实际被记录时才进行字符串格式化
    /// - 自动隐私保护：敏感信息会被自动处理
    /// - 系统集成：可在 Console.app 中查看，支持过滤和搜索
    ///
    /// - Parameters:
    ///   - message: 要记录的消息内容
    ///   - level: 日志级别，默认为 .info
    ///   - category: 日志分类，用于在系统日志中进行分组，默认为 "General"
    ///   - file: 发起日志记录的文件（自动获取）
    ///   - function: 发起日志记录的函数名（自动获取）
    ///   - line: 发起日志记录的代码行号（自动获取）
    static func log(
        _ message: String,
        level: LogLevel = .info,
        category: String = defaultCategory,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        // 检查是否启用日志记录以及日志级别过滤
        guard isEnabled && level.rawValue <= logLevel.rawValue else {
            return
        }

        // 获取文件名（不包含路径）
        let fileName = URL(fileURLWithPath: file).lastPathComponent

        // 获取对应分类的 OSLog 实例
        let osLog = logger(for: category)

        // 使用 os_log 进行日志记录
        // 注意：os_log 使用延迟字符串插值，性能优于传统的字符串拼接
        os_log(
            "%{public}@ [%{public}@:%{public}d] %{public}@: %{public}@",
            log: osLog,
            type: level.osLogType,
            level.emoji,
            fileName,
            line,
            function,
            message
        )

        // 在调试模式下同时输出到控制台，便于 Xcode 调试
        #if DEBUG
        print("\(level.emoji) \(level.description) [\(fileName):\(line)] \(function): \(message)")
        #endif
    }

    /// 带有自定义 OSLog 实例的日志记录方法
    ///
    /// 此方法允许使用预先创建的 OSLog 实例进行日志记录，
    /// 适用于需要特定配置或频繁使用的场景。
    ///
    /// - Parameters:
    ///   - message: 要记录的消息内容
    ///   - level: 日志级别
    ///   - osLog: 自定义的 OSLog 实例
    ///   - file: 发起日志记录的文件（自动获取）
    ///   - function: 发起日志记录的函数名（自动获取）
    ///   - line: 发起日志记录的代码行号（自动获取）
    static func log(
        _ message: String,
        level: LogLevel,
        osLog: OSLog,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        guard isEnabled && level.rawValue <= logLevel.rawValue else {
            return
        }

        let fileName = URL(fileURLWithPath: file).lastPathComponent

        os_log(
            "%{public}@ [%{public}@:%{public}d] %{public}@: %{public}@",
            log: osLog,
            type: level.osLogType,
            level.emoji,
            fileName,
            line,
            function,
            message
        )

        #if DEBUG
        print("\(level.emoji) \(level.description) [\(fileName):\(line)] \(function): \(message)")
        #endif
    }

    // MARK: - 便捷日志记录方法

    /// 记录错误级别日志的便捷方法
    ///
    /// 用于记录应用程序中的错误信息，这些日志会被持久化存储，
    /// 并在 Console.app 中以红色显示，便于问题排查。
    ///
    /// - Parameters:
    ///   - message: 错误消息内容
    ///   - category: 日志分类，默认为 "General"
    ///   - file: 发起日志记录的文件（自动获取）
    ///   - function: 发起日志记录的函数名（自动获取）
    ///   - line: 发起日志记录的代码行号（自动获取）
    static func error(
        _ message: String,
        category: String = defaultCategory,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(message, level: .error, category: category, file: file, function: function, line: line)
    }

    /// 记录警告级别日志的便捷方法
    ///
    /// 用于记录潜在的问题或需要注意的情况，
    /// 这些信息对于预防问题和优化应用性能很有帮助。
    ///
    /// - Parameters:
    ///   - message: 警告消息内容
    ///   - category: 日志分类，默认为 "General"
    ///   - file: 发起日志记录的文件（自动获取）
    ///   - function: 发起日志记录的函数名（自动获取）
    ///   - line: 发起日志记录的代码行号（自动获取）
    static func warning(
        _ message: String,
        category: String = defaultCategory,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(message, level: .warning, category: category, file: file, function: function, line: line)
    }

    /// 记录信息级别日志的便捷方法
    ///
    /// 用于记录应用程序的重要信息和状态变化，
    /// 这些日志有助于了解应用的运行状态。
    ///
    /// - Parameters:
    ///   - message: 信息消息内容
    ///   - category: 日志分类，默认为 "General"
    ///   - file: 发起日志记录的文件（自动获取）
    ///   - function: 发起日志记录的函数名（自动获取）
    ///   - line: 发起日志记录的代码行号（自动获取）
    static func info(
        _ message: String,
        category: String = defaultCategory,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(message, level: .info, category: category, file: file, function: function, line: line)
    }

    /// 记录调试级别日志的便捷方法
    ///
    /// 用于记录调试信息，这些日志仅在调试模式下显示，
    /// 有助于开发过程中的问题定位和功能验证。
    ///
    /// - Parameters:
    ///   - message: 调试消息内容
    ///   - category: 日志分类，默认为 "General"
    ///   - file: 发起日志记录的文件（自动获取）
    ///   - function: 发起日志记录的函数名（自动获取）
    ///   - line: 发起日志记录的代码行号（自动获取）
    static func debug(
        _ message: String,
        category: String = defaultCategory,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(message, level: .debug, category: category, file: file, function: function, line: line)
    }

    /// 记录详细级别日志的便捷方法
    ///
    /// 用于记录最详细的日志信息，通常用于深度调试和性能分析。
    /// 这些日志包含大量细节，仅在需要时启用。
    ///
    /// - Parameters:
    ///   - message: 详细消息内容
    ///   - category: 日志分类，默认为 "General"
    ///   - file: 发起日志记录的文件（自动获取）
    ///   - function: 发起日志记录的函数名（自动获取）
    ///   - line: 发起日志记录的代码行号（自动获取）
    static func verbose(
        _ message: String,
        category: String = defaultCategory,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(message, level: .verbose, category: category, file: file, function: function, line: line)
    }

    // MARK: - 专用分类日志方法

    /// 相机相关日志记录
    /// 专门用于记录相机操作、配置和状态相关的日志
    static func camera(_ message: String, level: LogLevel = .info, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: level, category: "Camera", file: file, function: function, line: line)
    }

    /// 姿态检测相关日志记录
    /// 专门用于记录 MediaPipe 姿态检测相关的日志
    static func poseDetection(_ message: String, level: LogLevel = .info, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: level, category: "PoseDetection", file: file, function: function, line: line)
    }

    /// 运动检测相关日志记录
    /// 专门用于记录设备运动、陀螺仪数据相关的日志
    static func motion(_ message: String, level: LogLevel = .info, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: level, category: "Motion", file: file, function: function, line: line)
    }

    /// 计数器相关日志记录
    /// 专门用于记录仰卧起坐等运动计数相关的日志
    static func counter(_ message: String, level: LogLevel = .info, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: level, category: "Counter", file: file, function: function, line: line)
    }

    /// UI 相关日志记录
    /// 专门用于记录用户界面操作和状态相关的日志
    static func ui(_ message: String, level: LogLevel = .info, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: level, category: "UI", file: file, function: function, line: line)
    }

    // MARK: - 工具方法

    /// 获取指定分类的 OSLog 实例（公开方法）
    ///
    /// 此方法允许外部代码获取特定分类的 OSLog 实例，
    /// 用于需要直接使用 os_log 的高级场景。
    ///
    /// - Parameter category: 日志分类名称
    /// - Returns: 对应分类的 OSLog 实例
    static func osLog(for category: String) -> OSLog {
        return logger(for: category)
    }

    /// 清理日志缓存
    ///
    /// 清理内部的 OSLog 实例缓存，释放内存。
    /// 通常在应用生命周期的特定时刻调用，如内存警告时。
    static func clearCache() {
        loggers.removeAll()
    }
}

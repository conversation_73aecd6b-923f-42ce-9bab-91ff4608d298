//
//  DebugLoggerExample.swift
//  FitCount
//
//  Created by AI Assistant on 2025/5/27.
//  示例代码：展示如何使用重构后的 DebugLogger
//

import Foundation

/// DebugLogger 使用示例类
/// 
/// 此类展示了如何在实际项目中使用重构后的 DebugLogger，
/// 包括各种日志级别、分类和特殊方法的使用。
class DebugLoggerExample {
    
    /// 演示基本日志记录功能
    static func demonstrateBasicLogging() {
        DebugLogger.info("🚀 开始演示基本日志记录功能")
        
        // 基本日志级别演示
        DebugLogger.error("这是一个错误级别的日志")
        DebugLogger.warning("这是一个警告级别的日志")
        DebugLogger.info("这是一个信息级别的日志")
        DebugLogger.debug("这是一个调试级别的日志")
        DebugLogger.verbose("这是一个详细级别的日志")
        
        DebugLogger.info("✅ 基本日志记录演示完成")
    }
    
    /// 演示分类日志记录功能
    static func demonstrateCategoryLogging() {
        DebugLogger.info("🏷️ 开始演示分类日志记录功能")
        
        // 使用专用分类方法
        DebugLogger.camera("相机初始化完成", level: .info)
        DebugLogger.poseDetection("检测到人体关键点", level: .debug)
        DebugLogger.motion("陀螺仪数据更新", level: .verbose)
        DebugLogger.counter("仰卧起坐计数: 10", level: .info)
        DebugLogger.ui("切换到运动模式", level: .info)
        
        // 使用自定义分类
        DebugLogger.info("自定义分类日志", category: "CustomCategory")
        DebugLogger.debug("数据库操作完成", category: "Database")
        DebugLogger.warning("网络连接不稳定", category: "Network")
        
        DebugLogger.info("✅ 分类日志记录演示完成")
    }
    
    /// 演示 Console.app 可见性增强功能
    static func demonstrateConsoleVisibility() {
        DebugLogger.info("👁️ 开始演示 Console.app 可见性增强功能")
        
        // 强制可见日志
        DebugLogger.forceLog("这条日志会在 Console.app 中强制显示")
        
        // 专用强制日志方法
        DebugLogger.appLaunch("应用启动耗时: 1.2秒")
        DebugLogger.performance("内存使用: 32MB")
        DebugLogger.network("API 请求成功，耗时: 150ms")
        
        DebugLogger.info("✅ Console.app 可见性增强演示完成")
    }
    
    /// 演示日志系统测试功能
    static func demonstrateLoggerTesting() {
        DebugLogger.info("🧪 开始演示日志系统测试功能")
        
        // 测试所有日志级别
        DebugLogger.testAllLevels()
        
        // 打印配置信息
        DebugLogger.printConfiguration()
        
        DebugLogger.info("✅ 日志系统测试演示完成")
    }
    
    /// 演示实际使用场景
    static func demonstrateRealWorldUsage() {
        DebugLogger.info("🌍 开始演示实际使用场景")
        
        // 模拟应用启动流程
        simulateAppLaunch()
        
        // 模拟相机操作
        simulateCameraOperations()
        
        // 模拟姿态检测
        simulatePoseDetection()
        
        // 模拟错误处理
        simulateErrorHandling()
        
        DebugLogger.info("✅ 实际使用场景演示完成")
    }
    
    // MARK: - 私有辅助方法
    
    private static func simulateAppLaunch() {
        DebugLogger.appLaunch("应用开始启动")
        
        // 模拟初始化步骤
        DebugLogger.info("正在初始化核心组件", category: "Initialization")
        DebugLogger.debug("加载配置文件", category: "Initialization")
        DebugLogger.info("初始化相机服务", category: "Initialization")
        DebugLogger.info("初始化姿态检测服务", category: "Initialization")
        
        DebugLogger.appLaunch("应用启动完成，总耗时: 0.8秒")
    }
    
    private static func simulateCameraOperations() {
        DebugLogger.camera("开始相机操作流程")
        
        // 模拟相机权限检查
        DebugLogger.camera("检查相机权限", level: .debug)
        DebugLogger.camera("相机权限已授权", level: .info)
        
        // 模拟相机配置
        DebugLogger.camera("配置相机参数", level: .debug)
        DebugLogger.camera("设置分辨率: 1920x1080", level: .verbose)
        DebugLogger.camera("设置帧率: 30fps", level: .verbose)
        
        // 模拟相机启动
        DebugLogger.camera("启动相机预览", level: .info)
        DebugLogger.camera("相机预览启动成功", level: .info)
    }
    
    private static func simulatePoseDetection() {
        DebugLogger.poseDetection("开始姿态检测流程")
        
        // 模拟模型加载
        DebugLogger.poseDetection("加载 MediaPipe 模型", level: .debug)
        DebugLogger.poseDetection("模型加载完成", level: .info)
        
        // 模拟检测过程
        DebugLogger.poseDetection("开始实时姿态检测", level: .info)
        DebugLogger.poseDetection("检测到人体，置信度: 0.95", level: .debug)
        DebugLogger.poseDetection("提取到 33 个关键点", level: .verbose)
        
        // 模拟计数逻辑
        DebugLogger.counter("检测到躺下姿态", level: .debug)
        DebugLogger.counter("检测到起身姿态", level: .debug)
        DebugLogger.counter("仰卧起坐计数 +1，当前: 5", level: .info)
    }
    
    private static func simulateErrorHandling() {
        // 模拟各种错误情况
        DebugLogger.warning("网络连接不稳定，正在重试", category: "Network")
        DebugLogger.error("相机权限被拒绝", category: "Camera")
        DebugLogger.error("姿态检测模型加载失败", category: "PoseDetection")
        
        // 模拟恢复操作
        DebugLogger.info("正在尝试恢复相机权限", category: "Camera")
        DebugLogger.info("重新加载姿态检测模型", category: "PoseDetection")
        DebugLogger.info("系统恢复正常", category: "Recovery")
    }
    
    /// 运行完整的演示
    static func runFullDemo() {
        DebugLogger.forceLog("🎬 开始 DebugLogger 完整演示")
        
        demonstrateBasicLogging()
        demonstrateCategoryLogging()
        demonstrateConsoleVisibility()
        demonstrateLoggerTesting()
        demonstrateRealWorldUsage()
        
        DebugLogger.forceLog("🎉 DebugLogger 完整演示结束")
        
        // 最后打印使用提示
        let tips = """
        
        💡 查看日志的方法：
        1. Xcode 控制台：运行应用时在 Xcode 底部查看
        2. Console.app：搜索 'com.jack.FitCount' 查看系统日志
        3. 真机调试：连接设备后在 Console.app 中选择对应设备
        
        🔍 如果在 Console.app 中看不到日志，请：
        - 确保选择了正确的设备
        - 搜索 'com.jack.FitCount' 或 'FitCount'
        - 检查时间范围设置
        - 使用 forceLog() 方法记录关键信息
        """
        
        DebugLogger.forceLog(tips)
    }
}
